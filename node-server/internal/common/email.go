package common

const (
	EmailContentTypeHtml = "text/html"
	EmailContentTypeText = "text/plain"
)

const AlarmEmailTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算云警报通知</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            text-align: center;
            margin: -20px -20px 20px -20px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .summary {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .summary-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }
        .summary-label {
            font-weight: bold;
            color: #856404;
        }
        .summary-value {
            color: #856404;
            margin-left: 5px;
        }
        .alarm-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .alarm-table th {
            background-color: #f8f9fa;
            color: #495057;
            font-weight: bold;
            padding: 12px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
        }
        .alarm-table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: top;
        }
        .alarm-table tr:hover {
            background-color: #f8f9fa;
        }
        .alarm-key {
            font-weight: bold;
            color: #dc3545;
            word-break: break-all;
        }
        .alarm-content {
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-family: "Courier New", monospace;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
            word-break: break-all;
        }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
        }
        .footer p {
            margin: 5px 0;
        }
        .warning-icon {
            color: #ffc107;
            font-size: 18px;
            margin-right: 5px;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            .summary-item {
                display: block;
                margin-right: 0;
            }
            .alarm-table {
                font-size: 14px;
            }
            .alarm-table th,
            .alarm-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="warning-icon">⚠️</span>算云警报通知</h1>
        </div>

        <div class="summary">
            <div class="summary-item">
                <span class="summary-label">警报数量:</span>
                <span class="summary-value">{{.Count}} 条</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">发送时间:</span>
                <span class="summary-value">{{.SendTime}}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">服务器IP:</span>
                <span class="summary-value">{{.LocalIP}}</span>
            </div>
        </div>

        {{if .AlarmList}}
        <table class="alarm-table">
            <thead>
                <tr>
                    <th style="width: 40%;">警报标识</th>
                    <th style="width: 60%;">详细内容</th>
                </tr>
            </thead>
            <tbody>
                {{range $index, $alarm := .AlarmList}}
                <tr>
                    <td class="alarm-key">{{$alarm.Key}}</td>
                    <td><div class="alarm-content">{{$alarm.Content}}</div></td>
                </tr>
                {{end}}
            </tbody>
        </table>
        {{end}}

        <div class="footer">
            <p><strong>晨羽智云（杭州）科技有限公司</strong></p>
            <p>地址：浙江省杭州市西湖区教工路88号 10楼1006室</p>
            <p>联系电话：132-0571-2110 | 邮箱：<EMAIL></p>
            <p style="margin-top: 15px; color: #999;">
                此邮件为系统自动发送，请勿直接回复。如有疑问，请联系技术支持。
            </p>
        </div>
    </div>
</body>
</html>
`

const ContainerFailureEmailTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>容器启动失败通知</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            text-align: center;
            margin: -20px -20px 20px -20px;
        }
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: bold;
        }
        .alert-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: #f8f9fa;
            border-radius: 6px;
            overflow: hidden;
        }
        .info-table th,
        .info-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .info-table th {
            background-color: #e9ecef;
            font-weight: bold;
            color: #495057;
            width: 30%;
        }
        .info-table td {
            background-color: #fff;
            color: #212529;
        }
        .info-table tr:last-child th,
        .info-table tr:last-child td {
            border-bottom: none;
        }
        .log-section {
            background-color: #f8f9fa;
            color: #495057;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #6c757d;
            margin: 20px 0;
            font-family: "Courier New", monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-word;
            max-height: 300px;
            overflow-y: auto;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
        }
        .footer p {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="alert-icon">🚨</span>{{.FailureType}}通知</h1>
        </div>

        <table class="info-table">
            <tr>
                <th>Pod名称</th>
                <td>{{.PodTitle}}</td>
            </tr>
            <tr>
                <th>报警时间</th>
                <td>{{.AlarmTime}}</td>
            </tr>
            <tr>
                <th>启动标记</th>
                <td>{{.StartupMark}}</td>
            </tr>
            <tr>
                <th>实例UUID</th>
                <td>{{.InstanceUuid}}</td>
            </tr>
            {{if .DockerID}}
            <tr>
                <th>容器ID</th>
                <td>{{.DockerID}}</td>
            </tr>
            {{end}}
            <tr>
                <th>节点ID</th>
                <td>{{.NodeID}}</td>
            </tr>
            <tr>
                <th>虚拟机ID</th>
                <td>{{.VirtualID}}</td>
            </tr>
            <tr>
                <th>虚拟机地址</th>
                <td>{{.VirtualHost}}</td>
            </tr>
            <tr>
                <th>Pod ID</th>
                <td>{{.PodID}}</td>
            </tr>
            <tr>
                <th>镜像ID</th>
                <td>{{.ImageID}}</td>
            </tr>
            {{if .ElapsedTime}}
            <tr>
                <th>耗时</th>
                <td>{{.ElapsedTime}}</td>
            </tr>
            {{end}}
        </table>

        {{if .StartupLog}}
        <div class="log-section">
            <strong>启动日志：</strong><br>
            {{.StartupLog}}
        </div>
        {{end}}

        <div class="footer">
            <p><strong>晨羽智云（杭州）科技有限公司</strong></p>
            <p>此邮件为系统自动发送，请及时处理相关问题。</p>
        </div>
    </div>
</body>
</html>
`
