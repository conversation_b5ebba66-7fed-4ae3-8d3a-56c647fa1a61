package service

import (
	"encoding/json"
	"fmt"
	"gopkg.in/gomail.v2"
	"html/template"
	"node-server/internal/common"
	"node-server/internal/common/jsontime"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/config"
	"node-server/internal/enums"
	"runtime"
	"strings"
	"sync"
	"time"
)

type email_ struct {
	SameSubject map[string]time.Time `json:"same_subject"`
	NeedSend    sync.Map
}

var EmailService email_

type EmailReq struct {
	From        string `json:"from"`
	To          string `json:"to"`
	Subject     string `json:"subject"`
	Content     string `json:"content"`
	ContentType string `json:"content_type"`
}

type emailItem struct {
	ID        uint      `json:"id"`
	Type      int       `json:"type"`
	Content   string    `json:"content"`
	UpdatedAt time.Time `json:"-"`
}

// AlarmEmailData 警报邮件数据结构
type AlarmEmailData struct {
	Count     int                    `json:"count"`
	SendTime  string                 `json:"send_time"`
	LocalIP   string                 `json:"local_ip"`
	AlarmList []AlarmItem            `json:"alarm_list"`
	RawData   map[string]interface{} `json:"raw_data"`
}

// AlarmItem 单个警报项
type AlarmItem struct {
	Key     string      `json:"key"`
	Content interface{} `json:"content"`
}

// ContainerFailureEmailData 容器启动失败邮件数据结构
type ContainerFailureEmailData struct {
	FailureType  string `json:"failure_type"`
	PodTitle     string `json:"pod_title"`
	AlarmTime    string `json:"alarm_time"`
	StartupMark  string `json:"startup_mark"`
	InstanceUuid string `json:"instance_uuid"`
	DockerID     string `json:"docker_id"`
	NodeID       uint   `json:"node_id"`
	VirtualID    uint   `json:"virtual_id"`
	VirtualHost  string `json:"virtual_host"`
	PodID        uint   `json:"pod_id"`
	ImageID      uint   `json:"image_id"`
	ElapsedTime  string `json:"elapsed_time"`
	StartupLog   string `json:"startup_log"`
}

// ContainerInstanceCheckEmailData 容器实例检查邮件数据结构
type ContainerInstanceCheckEmailData struct {
	CheckTime        string `json:"check_time"`
	VirtualHost      string `json:"virtual_host"`
	VirtualID        uint   `json:"virtual_id"`
	ContainerDetails string `json:"container_details"`
}

func (obj *email_) AddNeedSend(key string, value interface{}) {
	obj.NeedSend.Store(key, value)
}

func (obj *email_) ClearNeedSend() {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	body := ""
	tmpMap := make(map[string]interface{})
	count := 0
	obj.NeedSend.Range(func(key, value interface{}) bool {
		bSend := true
		stringKey := key.(string)
		redisKey := enums.RedisKeyEnum.AlarmKey + stringKey
		if val, _ := common.RedisGet(redisKey); val != "" {
			bSend = false
		}

		if bSend {
			//tmpMap[stringKey] = value
			body += stringKey + " " + utils.GetJsonFromStruct(value) + "\n"
			count++
			if count > 50 { //不能发太多
				return false
			}
		}
		obj.NeedSend.Delete(stringKey)
		return true // 继续遍历
	})
	//logger.Info("发送内容：", utils.GetJsonFromStruct(tmpMap))
	if len(tmpMap) > 0 {
		// 生成HTML格式的邮件内容
		htmlContent, err := obj.generateAlarmEmailHTML(tmpMap)
		if err != nil {
			logger.Error("生成HTML邮件内容失败:", err)
			// 如果HTML生成失败，回退到原来的JSON格式
			htmlContent = fmt.Sprintf("<pre>%s</pre>", utils.GetJsonFromStruct(tmpMap))
		}

		emailReq := EmailReq{
			From:        "",
			To:          config.AlarmEmailAddr,
			Subject:     fmt.Sprintf("有%d条算云警报，发送时间:%s", len(tmpMap), jsontime.Now().String()),
			Content:     htmlContent,
			ContentType: common.EmailContentTypeHtml, // 设置为HTML格式
		}
		if err := EmailService.SendFromServiceWitContentType(emailReq); err != nil {
			logger.Error("发送HTML格式警报邮件失败:", err)
		}
	}
}

func (obj *email_) SendFromServiceWitContentType(oReq EmailReq) error {
	msg := ""
	result := make(map[string]interface{})

	if strings.Contains(utils.GetLocalIP(), "200.3") {
		return nil
	}
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			msg = "执行失败"
			logger.Error(msg, e, result, e)
		}
	}()

	m := gomail.NewMessage()

	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", oReq.To)
	m.SetHeader("Subject", oReq.Subject)
	//m.SetBody("text/html", oReq.Content)
	m.SetBody(oReq.ContentType, oReq.Content)

	d := gomail.NewDialer("smtp.feishu.cn", 465, "<EMAIL>", "psK3xJEkMchq6uwY")
	if err := d.DialAndSend(m); err != nil {
		msg = "邮件发送失败"
		logger.Error(msg, " ", oReq.Subject, " ", err)
	} else {
		logger.Info("邮件发送成功")
	}

	return nil
}

func (obj *email_) Send(oReq EmailReq) error {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	if strings.Contains(utils.GetLocalIP(), "200.3") {
		return nil
	}
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
			logger.Error(code, msg, e, result, e)
		}
	}()

	m := gomail.NewMessage()
	//m.SetHeader("From", "<EMAIL>")
	//m.SetHeader("To", "<EMAIL>")
	//m.SetHeader("Subject", "邮件发送测试")
	//m.SetBody("text/html", "邮件发送测试内容Example Message Body")

	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", oReq.To)
	m.SetHeader("Subject", oReq.Subject)
	m.SetBody("text/plain", oReq.Content)

	d := gomail.NewDialer("smtp.163.com", 25, "guluguluxiaozi", "QESLVYWCPGINYOJP")
	if err := d.DialAndSend(m); err != nil {
		msg = "邮件发送失败"
		logger.Error(msg, " ", oReq.Subject, " ", err)
		return err
	} else {
		logger.Info("邮件发送成功")
	}
	code = 0
	return nil
}

func (obj *email_) SendWarn(oReq EmailReq) error {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	if strings.Contains(utils.GetLocalIP(), "200.3") {
		return nil
	}
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
			logger.Error(code, msg, e, result, e)
		}
	}()

	m := gomail.NewMessage()

	m.SetHeader("To", oReq.To)
	m.SetHeader("Subject", oReq.Subject+" ip:"+utils.GetLocalIP())
	content := strings.ReplaceAll(oReq.Content, `\\\"`, `"`)
	content = strings.ReplaceAll(content, `\\"`, `"`)
	content = strings.ReplaceAll(content, `\"`, `"`)
	//htmlContent := html.UnescapeString(content) // 确保 <br/> 不被转义
	//m.SetBody("text/html", htmlContent)
	m.SetBody("text/plain", content)

	var err error
	{
		m.SetHeader("From", "<EMAIL>")
		m.SetHeader("To", "<EMAIL>")
		d := gomail.NewDialer("smtp.163.com", 25, "guluguluxiaozi", "QESLVYWCPGINYOJP")
		if err = d.DialAndSend(m); err != nil {
			msg = "邮件发送失败"
			logger.Error(msg, " ", oReq.Subject, " ", err)
		} else {
			logger.Info("邮件发送成功")
		}
	}

	{
		m.SetHeader("From", "<EMAIL>")
		m.SetHeader("To", "<EMAIL>")
		d := gomail.NewDialer("smtp.feishu.cn", 465, "<EMAIL>", "psK3xJEkMchq6uwY")
		if err = d.DialAndSend(m); err != nil {
			msg = "邮件发送失败"
			logger.Error(msg, " ", oReq.Subject, " ", err)
		} else {
			logger.Info("邮件发送成功")
		}

	}
	if err == nil {
		code = 0
	}
	return err
}

// generateAlarmEmailHTML 生成警报邮件的HTML内容
func (obj *email_) generateAlarmEmailHTML(alarmData map[string]interface{}) (string, error) {
	// 解析模板
	tmpl, err := template.New("alarmEmail").Parse(common.AlarmEmailTemplate)
	if err != nil {
		return "", fmt.Errorf("解析邮件模板失败: %v", err)
	}

	// 准备模板数据
	emailData := AlarmEmailData{
		Count:    len(alarmData),
		SendTime: jsontime.Now().String(),
		LocalIP:  utils.GetLocalIP(),
		RawData:  alarmData,
	}

	// 转换警报数据为结构化格式
	for key, value := range alarmData {
		alarmItem := AlarmItem{
			Key: key,
		}

		// 格式化内容
		if valueStr, ok := value.(string); ok {
			alarmItem.Content = valueStr
		} else {
			// 如果不是字符串，转换为JSON格式
			if jsonBytes, err := json.MarshalIndent(value, "", "  "); err == nil {
				alarmItem.Content = string(jsonBytes)
			} else {
				alarmItem.Content = fmt.Sprintf("%v", value)
			}
		}

		emailData.AlarmList = append(emailData.AlarmList, alarmItem)
	}

	// 执行模板
	var htmlBuffer strings.Builder
	if err := tmpl.Execute(&htmlBuffer, emailData); err != nil {
		return "", fmt.Errorf("执行邮件模板失败: %v", err)
	}

	return htmlBuffer.String(), nil
}

// generateContainerFailureEmailHTML 生成容器启动失败邮件的HTML内容
func (obj *email_) generateContainerFailureEmailHTML(data ContainerFailureEmailData) (string, error) {
	// 解析模板
	tmpl, err := template.New("containerFailureEmail").Parse(common.ContainerFailureEmailTemplate)
	if err != nil {
		return "", fmt.Errorf("解析邮件模板失败: %v", err)
	}

	// 执行模板
	var htmlBuffer strings.Builder
	if err := tmpl.Execute(&htmlBuffer, data); err != nil {
		return "", fmt.Errorf("执行邮件模板失败: %v", err)
	}

	return htmlBuffer.String(), nil
}
