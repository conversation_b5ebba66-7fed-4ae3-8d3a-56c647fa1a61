package service

import (
	"errors"
	"node-server/internal/common"
	"node-server/internal/common/jsontime"
	"node-server/internal/common/logger"
	"node-server/internal/common/utils"
	"node-server/internal/enums"
	"strings"
	"time"
)

type startuplog_ struct {
}

var StartupLog startuplog_

func (d *startuplog_) Save(startupMark string, showmsg string, logtxt string) (int64, error) {

	if showmsg == "" {
		if arr, err := d.Last(startupMark); err != nil {
			logger.Error(err, startupMark)
		} else {
			if len(arr) > 0 {
				m := utils.GetMapFromJson(arr[0])
				if _, ok := m["msg"]; ok {
					showmsg = m["msg"].(string)
				}
			}
		}
	}

	result := make(map[string]interface{})
	result["time"] = time.Now().Format(jsontime.TimeFormat)
	result["msg"] = showmsg
	result["log"] = logtxt
	result["status"] = 0
	if strings.Contains(showmsg, "启动成功") {
		result["status"] = 1
	}
	if strings.Contains(showmsg, "启动失败") {
		result["status"] = 2
	}
	txt := utils.GetJsonFromStruct(result)
	key := enums.RedisKeyEnum.CpnSchedStartupLog + startupMark
	//logger.Info("startuplog:", key, "===", txt)
	return common.RedisRPush(key, txt)
}

func (d *startuplog_) List(startupMark string) ([]string, error) {
	key := enums.RedisKeyEnum.CpnSchedStartupLog + startupMark
	return common.RedisLRange(key, 0, -1)
}

func (d *startuplog_) Last(startupMark string) ([]string, error) {
	arr := make([]string, 0)
	key := enums.RedisKeyEnum.CpnSchedStartupLog + startupMark
	// 获取列表的长度
	length, err := common.RedisLLen(key)
	if err != nil {
		logger.Error(err)
		return arr, err
	}
	// 如果列表为空，直接返回空字符串
	if length == 0 {
		return arr, nil
	}

	ary, err := common.RedisLRange(key, length-1, length-1)
	if err != nil {
		logger.Error(err)
		return arr, err
	}
	return ary, nil
	//if ary[0] != "" {
	//	return utils.GetMapFromJson(ary[0]), nil
	//}
	//
	//return m, nil
}

func (d *startuplog_) BootIn(startupMark string) (bool, error) {
	if arr, err := d.Last(startupMark); err != nil {
		msg := "获取启动日志失败"
		logger.Error(msg, err)
		return true, err
	} else {
		if len(arr) == 0 {
			logger.Info("启动日志为空，认为没有正在启动中 startupMark:", startupMark)
			return false, nil
		}
		m := utils.GetMapFromJson(arr[0])
		if m != nil {
			if _, ok := m["status"]; ok {
				status := int(m["status"].(float64))
				if status == 1 {
					return false, nil
				}
				if status == 2 {
					return false, nil
				}
			}
			if _, ok := m["time"]; ok {
				timeObj, err := time.ParseInLocation(jsontime.TimeFormat, m["time"].(string), time.Local)
				if err != nil {
					logger.Error("转换时间失败， startupMark:", startupMark, "   ", err, arr[0])
					return true, err
				} else {
					checkTime := time.Now().Add(-time.Minute * 3)
					if timeObj.After(checkTime) {
						logger.Info("startupMark:", startupMark, "还在启动中，不检查 checkTime:", checkTime, "   timeObj:", timeObj, "   json:", arr[0], "  m[\"time\"].(string):", m["time"].(string))
						return true, nil
					} else {
						return false, nil
					}
				}
			}
		}
	}
	return true, errors.New("未获取到日志时间")
}

func (d *startuplog_) Delete(startupMark string) error {
	key := enums.RedisKeyEnum.CpnSchedStartupLog + startupMark
	return common.RedisDel(key)
}

func (d *startuplog_) ScanAndHandleKeys() error {
	keys, err := common.RedisScanKeys(enums.RedisKeyEnum.CpnSchedStartupLog + "*")
	if err != nil {
		logger.Error(err)
		return err
	}
	logger.Info("ScanAndHandleKeys 共检索出", len(keys), "条记录")
	for _, key := range keys {
		ary, err1 := common.RedisLRange(key, -1, -1)
		if err1 != nil || len(ary) == 0 {
			logger.Error(err, key)
		} else {
			m := utils.GetMapFromJson(ary[0])
			if m == nil {
				logger.Error("m is nil ", key, "   ", ary[0])
				continue
			}
			if _, ok := m["time"]; !ok {
				logger.Error("time is not exists ", key, "   ", ary[0])
				continue
			}

			timeObj, err := time.ParseInLocation(jsontime.TimeFormat, m["time"].(string), time.Local)
			if err != nil {
				logger.Error(err, key, "   ", ary[0])
				continue
			}
			twoDaysAgo := time.Now().AddDate(0, 0, -1)
			if timeObj.Before(twoDaysAgo) {
				if strings.HasPrefix(key, enums.RedisKeyEnum.CpnSchedStartupLog) {
					if err := common.RedisDel(key); err != nil {
						logger.Error("删除startup出错", key, "  ", ary[0])
					} else {
						//logger.Info("删除一条Startup日志，key：", key)
					}
				} else {
					logger.Error("非startup日志", key, "   ", ary[0])
				}
			}
		}
	}
	logger.Info("ScanAndHandleKeys 共检索出", len(keys), "条记录处理完成")
	return nil
}

func (d *startuplog_) SendEmail(subject string, startupMark string) error {

	content := ""
	ary, err := d.List(startupMark)
	if err != nil {
		content = "获取日志出错 " + err.Error()
	}
	content = utils.GetJsonFromStruct(ary)

	emailReq := EmailReq{
		From:    "",
		To:      "<EMAIL>",
		Subject: subject + time.Now().Format(jsontime.TimeFormat),
		Content: content,
	}
	EmailService.SendWarn(emailReq)
	return nil
}
