package config

import (
	"fmt"
	"gopkg.in/ini.v1"
)

var (
	LogLevel   string
	Env        string
	ClearNginx bool
	HttpPort   string

	PrivateStorage string
	MasterServer   string
	NodeId         uint
	NodeToken      string

	REDIS_CONN_STRING       string
	RELAY_REDIS_CONN_STRING string

	DaemonServerPort uint

	AlarmEmailAddr string
)

func init() {
	file, err := ini.Load("./config/config.ini")
	if err != nil {
		fmt.Println("配置文件读取错误", err)
	} else {
		LoadServer(file)
	}
}

func LoadServer(file *ini.File) {
	LogLevel = file.Section("server").Key("LogLevel").MustString("info")
	Env = file.Section("server").Key("Env").MustString("")

	ClearNginx = file.Section("server").Key("ClearNginx").MustBool(false)
	HttpPort = file.Section("server").Key("HttpPort").MustString("")

	PrivateStorage = file.Section("server").Key("PrivateStorage").MustString("")
	//PrivateStorage = "/mnt/user-data/store0/"
	MasterServer = file.Section("server").Key("MasterServer").MustString("")
	NodeId = file.Section("server").Key("NodeId").MustUint(0)
	NodeToken = file.Section("server").Key("NodeToken").MustString("")

	AlarmEmailAddr = file.Section("server").Key("AlarmEmailAddr").MustString("")

	REDIS_CONN_STRING = file.Section("database").Key("REDIS_CONN_STRING").MustString("")
	RELAY_REDIS_CONN_STRING = file.Section("database").Key("RELAY_REDIS_CONN_STRING").MustString("")

	DaemonServerPort = file.Section("server").Key("DaemonServerPort").MustUint(6789)
}
