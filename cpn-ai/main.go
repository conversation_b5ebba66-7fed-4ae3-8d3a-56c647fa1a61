package main

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/config"
	"cpn-ai/controller"
	"cpn-ai/controller/master"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/router"
	"cpn-ai/service"
	"cpn-ai/service/relay"
	"embed"
	"fmt"
	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
	"os"
)

//go:embed web/build
var buildFS embed.FS

//go:embed web/build/index.html
var indexPage []byte

func main() {
	logger.Info("启动main " + common.Version)
	common.SetupLogger()
	common.SysLog("Cpn Ai " + common.Version + " started")
	if os.Getenv("GIN_MODE") != "debug" {
		gin.SetMode(gin.ReleaseMode)
	}
	if common.DebugEnabled {
		common.SysLog("running in debug mode")
	}
	// Initialize SQL Database
	if err := model.InitDB(); err != nil {
		common.FatalLog("failed to initialize database: " + err.Error())
	}
	defer func() {
		err := model.CloseDB()
		if err != nil {
			common.FatalLog("failed to close database: " + err.Error())
		}
	}()

	// Initialize Redis
	if err := common.InitRedisClient(); err != nil {
		common.FatalLog("failed to initialize Redis: " + err.Error())
	}

	relay.InitTokenEncoders()

	// Initialize options
	model.InitOptionMap()
	if common.RedisEnabled {
		// for compatibility with old versions
		common.MemoryCacheEnabled = true
	}
	if common.MemoryCacheEnabled {
		common.SysLog("memory cache enabled")
		common.SysError(fmt.Sprintf("sync frequency: %d seconds", common.SyncFrequency))
		model.InitChannelCache()
	}
	if common.MemoryCacheEnabled {
		go model.SyncOptions(common.SyncFrequency)
		go model.SyncChannelCache(common.SyncFrequency)
	}
	common.SysLog("channels synced from database1111")
	if len(config.DiffusionFilePath) < 10 {
		logger.Error("配置参数异常 common.Config.DiffusionFilePath：", config.DiffusionFilePath)
		return
	}
	common.SysLog("channels synced from database1111.11111")
	//if err := service.CheckPrivateStorage(); err != nil {
	//	logger.Error("用户存储基础路径检测失败", err)
	//	return
	//}
	common.SysLog("channels synced from database1111.22222")
	if controller.CardApi.BindTest == nil {
		controller.CardApi.BindTest = make(map[string]int)
	}

	common.SysLog("channels synced from database2222")

	if master.ReportApi.PodImageIds == nil {
		master.ReportApi.PodImageIds = make(map[string]string)
	}

	//if service.SchedService.VirtualNodes == nil {
	//	if err := service.SchedService.Init(); err != nil {
	//		logger.Error("初始化调度信息失败", err)
	//		return
	//	}
	//}
	common.SysLog("channels synced from database3333")
	if config.Bundle == "cn.suanyun.www" || config.Bundle == "cn.chenyu.www" || config.Bundle == "team.chenyu.www" {
		service.RunTimer()
	}

	//if os.Getenv("CHANNEL_UPDATE_FREQUENCY") != "" {
	//	frequency, err := strconv.Atoi(os.Getenv("CHANNEL_UPDATE_FREQUENCY"))
	//	if err != nil {
	//		common.FatalLog("failed to parse CHANNEL_UPDATE_FREQUENCY: " + err.Error())
	//	}
	//	go controller.AutomaticallyUpdateChannels(frequency)
	//}
	//if os.Getenv("CHANNEL_TEST_FREQUENCY") != "" {
	//	frequency, err := strconv.Atoi(os.Getenv("CHANNEL_TEST_FREQUENCY"))
	//	if err != nil {
	//		common.FatalLog("failed to parse CHANNEL_TEST_FREQUENCY: " + err.Error())
	//	}
	//	go controller.AutomaticallyTestChannels(frequency)
	//}
	//if os.Getenv("BATCH_UPDATE_ENABLED") == "true" {
	//	common.BatchUpdateEnabled = true
	//	common.SysLog("batch update enabled with interval " + strconv.Itoa(common.BatchUpdateInterval) + "s")
	//	model.InitBatchUpdater()
	//}
	//controller.InitTokenEncoders()

	// Initialize HTTP server
	server := gin.New()
	server.Use(gin.Recovery())
	// This will cause SSE not to work!!!
	//server.Use(gzip.Gzip(gzip.DefaultCompression))
	server.Use(middleware.RequestId())
	middleware.SetUpLogger(server)
	// Initialize session store
	store := cookie.NewStore([]byte(common.SessionSecret))
	//store.Options(sessions.Options{
	//	Domain: ".chenyu.cn",
	//	Path:   "/",
	//	MaxAge: 7 * 86400,
	//})
	server.Use(sessions.Sessions("session", store))

	router.SetRouter(server, buildFS, indexPage)

	common.SysLog("开启端口监听..." + config.HttpPort)
	logger.Info("开启端口监听..." + config.HttpPort)
	if err := server.Run(config.HttpPort); err != nil {
		logger.Info("开启端口监听失败 port"+config.HttpPort, " err:", err.Error())
		common.FatalLog("failed to start HTTP server: " + err.Error())
	}
}
