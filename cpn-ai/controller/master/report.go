package master

import (
	"bytes"
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"sync"
	"text/template"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type reportApi_ struct {
	PodImageIds   map[string]string `json:"pod_image_ids"`
	Md5PodImageId sync.Map
}

var ReportApi reportApi_

type dockerPushReq struct {
	StartupMark string `json:"startup_mark"`
	ImageId     uint   `json:"image_id"`
	ReportState string `json:"report_state"`
	Task        string `json:"task"`
	Reason      string `json:"reason"`
	ImageMeta   string `json:"image_meta"`
}

type PodAlertData struct {
	PodName      string
	AlertTime    string
	StartTag     string
	InstanceUUID string
	DockerID     string
	NodeID       uint
	VirtualID    uint
	PodID        uint
	ImageID      uint
	LogContent   string
}
type inspectImageReq struct {
	VirtualId  uint `json:"virtual_id"`
	ImageId    uint `json:"image_id"`
	LayerCount int  `json:"layer_count"`
}

type localImagesReq struct {
	VirtualId uint   `json:"virtual_id"`
	Images    string `json:"images"`
	ImageIds  string `json:"image_ids"`
}

type virtualInfoReq struct {
	VirtualId     uint              `json:"virtual_id"`
	GpuFrees      int               `json:"gpu_frees"`
	TotalGpus     int               `json:"total_gpus"` //[0,0,1,0,1,0]
	TotalInstance int               `json:"total_instance"`
	InitAt        int64             `json:"init_at"`         //初始化开始时间
	LastCheckTime string            `json:"last_check_time"` //最后检测时间
	LastInitTime  jsontime.JsonTime `json:"last_init_time"`
	DockerAt      int64             `json:"docker_at"` //启动docker时间戳,0为没有在启动
	TimeoutAt     int64             `json:"timeout_at"`
}

type virtualTimeoutReq struct {
	VirtualId uint  `json:"virtual_id"`
	TimeoutAt int64 `json:"timeout_at"`
}

type nodeInfoReq struct {
	NodeId        uint `json:"node_id"`
	TotalVirtual  int  `json:"total_virtual"`
	TotalInstance int  `json:"total_instance"`
	TotalGpus     int  `json:"total_gpus"`
	FreeGpus      int  `json:"free_gpus"`
}

type startupSuccessReq struct {
	StartupMark string                 `json:"startup_mark"`
	Docker      map[string]interface{} `json:"docker"`
	Reason      string                 `json:"reason"`
}

func (obj *reportApi_) DockerCommit(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq dockerPushReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if err := service.ReportService.HandleDockerCommit(oReq.StartupMark, oReq.ImageId, oReq.ReportState); err != nil {
		msg = "上报失败"
		logger.Error(err, "oReq:", utils.GetJsonFromStruct(oReq))
		return
	} else {
		msg = "上报成功"
		code = 0
		return
	}
}

func (obj *reportApi_) DockerPush(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	var oReq dockerPushReq
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		ginH := gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		}

		lenMeta := len(oReq.ImageMeta)
		oReq.ImageMeta = ""
		logger.Info("DockerPush", " oReq:", utils.GetJsonFromStruct(oReq), " ImageMeta Len:", lenMeta, " ginH:", utils.GetJsonFromStruct(ginH))

		c.JSON(http.StatusOK, ginH)
	}()

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if err := service.ReportService.HandleDockerPush(oReq.StartupMark, oReq.ImageId, oReq.ReportState, oReq.Reason, oReq.ImageMeta); err != nil {
		msg = "上报处理失败"
		logger.Error(msg, " err:", err, "oReq:", utils.GetJsonFromStruct(oReq))
		return
	} else {
		msg = "上报处理成功"
		if oReq.ReportState == enums.ReportStateEnum.Success {
			if oReq.Task == tasklog.TaskEnum.SaveImageAndShutdown { //关闭实例

				/*
					go func() {
						var instance model.Instance
						if err := instance.GetByStartupMark(oReq.StartupMark); err != nil {
							logger.Error(err)
						} else {
							logger.Info("DockerPush oReq:", utils.GetJsonFromStruct(oReq))
							if txt, err := service.InstanceNodeService.Shutdown(instance.Uuid, enums.ShutdownReasonEnum.SaveImageAndShutdown, true); err != nil {
								msg = txt
								logger.Error(err)
							} else {
								if err := instance.GetByUuid(instance.Uuid); err != nil {
									msg = "获取实例信息失败"
									logger.Error(msg, err)
									return
								}
								if instance.Status == enums.InstanceStatusEnum.ShutdownComplete && instance.ShutdownReason == enums.ShutdownReasonEnum.SaveImageAndShutdown {
									if err := instance.SetStatus(enums.InstanceStatusEnum.Hidden); err != nil {
										msg = "删除实例失败"
										logger.Error(msg, err, instance.ID)
										return
									}
								}
							}
						}
					}()*/
			}
		}
		go func() {
			showObjName := "镜像"
			if oReq.Task == tasklog.TaskEnum.SaveInstanceImage {
				showObjName = "实例数据"
			}
			reason := showObjName + "保存失败"
			if oReq.ReportState == enums.ReportStateEnum.Success {
				reason = showObjName + "保存成功"
			}
			var instance model.Instance
			if err := instance.GetByStartupMark(oReq.StartupMark); err != nil {
				logger.Error(err, "  startupMark:", oReq.StartupMark)
			} else {
				if msg1, err1 := service.WarnService.InstanceRunning(instance.UserId, instance.Uuid, reason); err1 != nil {
					logger.Error("发送请求(实例运行提醒)失败 userId:", instance.ID, "  uuid:", instance.Uuid, "  reason:", reason, "  msg:", msg1, " err:", err1)
				} else {
					logger.Info("发送请求(实例运行提醒)成功 userId:", instance.ID, "  uuid:", instance.Uuid, "  reason:", reason)
				}
			}
			if oReq.ReportState == enums.ReportStateEnum.Fail {
				body := tasklog.SaveImageContent(utils.Uint2String(oReq.ImageId))

				podAlertData := PodAlertData{
					PodName:      instance.PodName,
					AlertTime:    time.Now().Format(jsontime.TimeFormat),
					StartTag:     instance.StartupMark,
					InstanceUUID: instance.Uuid,
					DockerID:     instance.DockerId,
					NodeID:       instance.StartupNodeId,
					VirtualID:    instance.StartupVirtualId,
					PodID:        instance.PodId,
					ImageID:      instance.ImageId,
					LogContent:   body,
				}
				tmpl, err := template.New("warnMail").Parse(common.WarnMail)
				if err != nil {
					logger.Error("模板解析错误: %v", err)
				}

				// 执行模板并将结果写入 buffer
				var buf bytes.Buffer
				if err = tmpl.Execute(&buf, podAlertData); err != nil {
					logger.Error("执行模板错误: %v", err)
				}

				var emailReq = service.EmailReq{
					To:          config.AlarmEmailAddr,
					Subject:     "POD异常提醒",
					Content:     buf.String(),
					ContentType: common.EmailContentTypeHtml,
				}
				service.EmailService.SendFromServiceWitContentType(emailReq)
			}
		}()

		code = 0
		return
	}
}

func (obj *reportApi_) VirtualLocalImages11(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq localImagesReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	result["virtual_id"] = oReq.VirtualId
	//logger.Info("oReq.Image:", oReq.Images)

	ary := make([]structs.DockerImage, 0)
	if err := utils.GetStructAryFromJson(&ary, oReq.Images); err != nil {
		logger.Error(err, "  oReq.Images:", oReq.Images)
		return
	}
	if len(ary) == 0 {
		msg = "上传Image数组为空"
		result["image_ids"] = ""
		code = 0
		return
	}

	hasError := false
	aryPodImage := make([]string, 0)
	for _, item := range ary {
		//logger.Info("item:", utils.GetJsonFromStruct(item))
		if item.ImageId > 0 {
			//logger.Info("imageId 大于0，无需查询 imageID:", item.ImageId)
			tmp := fmt.Sprintf("%d", item.ImageId)
			aryPodImage = append(aryPodImage, tmp)
			continue
		}
		if item.ID != "" {
			if val, ok := obj.PodImageIds[item.ID]; ok {
				//logger.Info("Id在缓存中，无需查询 ID:", item.ID)
				if val != "" {
					aryPodImage = append(aryPodImage, val)
					continue
				}
			} else {
				var podImage model.PodImage
				//logger.Info("开始查询镜像 ID:", item.ID)
				if err := podImage.GetBySha256(item.ID); err != nil {
					if err == gorm.ErrRecordNotFound {
						obj.PodImageIds[item.ID] = ""
					} else {
						hasError = true
						logger.Error(err, " podImage:", utils.GetJsonFromStruct(item))
					}
				} else {
					tmp := fmt.Sprintf("%d", podImage.ID)
					aryPodImage = append(aryPodImage, tmp)
					obj.PodImageIds[item.ID] = tmp
				}
			}
		}

		imageType := 0
		imageName := ""
		imageTag := ""
		repository := item.Repository
		if repository == "" {
			repository = item.HubImagePath
		}
		if strings.Contains(repository, "/private/") {
			imageType = enums.ImageTypeEnum.Private
		} else if strings.Contains(repository, "/public/") {
			imageType = enums.ImageTypeEnum.Public
		} else if strings.Contains(repository, "/suanyun-dev/") {
			imageType = enums.ImageTypeEnum.Base
		}
		tmpAry := strings.Split(repository, "/")

		if len(tmpAry) == 4 {
			tmp := tmpAry[3]
			if strings.Contains(tmp, ":") {
				tAry := strings.Split(tmp, ":")
				if len(tAry) == 2 {
					imageName = tAry[0]
					imageTag = tAry[1]
				}
			}
		} else {
			if len(tmpAry) == 3 {
				imageName = tmpAry[2]
			}
			if item.Tag != "" {
				imageTag = item.Tag
			}
		}

		md5 := fmt.Sprintf("%d:%s:%s", imageType, imageName, imageTag)
		if imageType > 0 && imageName != "" && imageTag != "" {
			if val, ok := obj.PodImageIds[md5]; ok {
				//logger.Info("md5在缓存中，无需查询 md5:", md5)
				if val != "" {
					aryPodImage = append(aryPodImage, val)
				}
				continue
			} else {
				var podImage model.PodImage
				//logger.Info("开始查询镜像", "imageType:", imageType, "  imageName:", imageName, "  imageTag:", imageTag)
				if err := podImage.GetByImageTypeAndTag(imageType, imageName, imageTag); err != nil {
					if err == gorm.ErrRecordNotFound {
						if imageType == enums.ImageTypeEnum.Private {
							imageType = enums.ImageTypeEnum.PrivateInstance
							if err := podImage.GetByImageTypeAndTag(imageType, imageName, imageTag); err != nil {
								if err == gorm.ErrRecordNotFound {
									obj.PodImageIds[md5] = ""
								} else {
									hasError = true
									logger.Error(err, "imageType:", imageType, "  imageName:", imageName, "  imageTag:", imageTag)
								}
							} else {
								tmp := fmt.Sprintf("%d", podImage.ID)
								aryPodImage = append(aryPodImage, tmp)
								obj.PodImageIds[md5] = tmp
								if item.ID != "" {
									obj.PodImageIds[item.ID] = tmp
								}
							}
						}
					} else {
						hasError = true
						logger.Error(err, "imageType:", imageType, "  imageName:", imageName, "  imageTag:", imageTag)
					}
				} else {
					tmp := fmt.Sprintf("%d", podImage.ID)
					aryPodImage = append(aryPodImage, tmp)
					obj.PodImageIds[md5] = tmp
					if item.ID != "" {
						obj.PodImageIds[item.ID] = tmp
					}
				}
			}
		} else {
			if repository != "" {
				logger.Error("未获取到相应信息 md5:", md5, "     ", "item:", utils.GetJsonFromStruct(item))
			}
		}
	}
	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		logger.Error(err, " virtualId:", oReq.VirtualId)
		msg = err.Error()
		return
	} else {
		imagesIds := ""
		if len(aryPodImage) > 0 {
			imagesIds = "|" + strings.Join(aryPodImage, "|") + "|"
		} else {
			msg = "空数组"
		}
		if hasError == false {
			if err := virtual.SetImageIds(imagesIds); err != nil {
				logger.Error(err, " virtualId:", oReq.VirtualId)
				msg = err.Error()
				return
			}
		}
	}

	if virtual.ID > 0 {
		result["image_ids"] = virtual.ImageIds
		code = 0
	} else {
		msg = "虚拟机不存在"
	}

	//{"id":"4fe667ce8b38","repository":"hub.suanyun.cn/suanyun-dev/cuda","tag":"12.3.2-cudnn9-devel-ubuntu22.04-jupyter"}
}

func (obj *reportApi_) VirtualLocalImages(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq localImagesReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	result["virtual_id"] = oReq.VirtualId
	//logger.Info("oReq.Image:", oReq.Images)

	ary := make([]structs.DockerImage, 0)
	if err := utils.GetStructAryFromJson(&ary, oReq.Images); err != nil {
		logger.Error(err, "  oReq.Images:", oReq.Images)
		return
	}
	if len(ary) == 0 {
		msg = "上传Image数组为空"
		result["image_ids"] = ""
		code = 0
		return
	}

	hasError := false
	aryPodImage := make([]string, 0)
	searchCount := 0
	for _, item := range ary {
		//logger.Info("item:", utils.GetJsonFromStruct(item))
		if item.ImageId > 0 {
			//logger.Info("imageId 大于0，无需查询 imageID:", item.ImageId)
			tmp := fmt.Sprintf("%d", item.ImageId)
			aryPodImage = append(aryPodImage, tmp)
			continue
		}
		/*
			if item.ID != "" {
				if val, ok := obj.PodImageIds[item.ID]; ok {
					//logger.Info("Id在缓存中，无需查询 ID:", item.ID)
					if val != "" {
						aryPodImage = append(aryPodImage, val)
						continue
					}
				} else {
					var podImage model.PodImage
					//logger.Info("开始查询镜像 ID:", item.ID)
					if err := podImage.GetBySha256(item.ID); err != nil {
						if err == gorm.ErrRecordNotFound {
							obj.PodImageIds[item.ID] = ""
						} else {
							hasError = true
							logger.Error(err, " podImage:", utils.GetJsonFromStruct(item))
						}
					} else {
						tmp := fmt.Sprintf("%d", podImage.ID)
						aryPodImage = append(aryPodImage, tmp)
						obj.PodImageIds[item.ID] = tmp
					}
				}
			}*/

		imageType := 0
		imageName := ""
		imageTag := ""
		repository := item.Repository
		if repository == "" {
			repository = item.HubImagePath
		}

		if strings.Contains(repository, "/private/") {
			imageType = enums.ImageTypeEnum.Private
		} else if strings.Contains(repository, "/public/") {
			imageType = enums.ImageTypeEnum.Public
		} else if strings.Contains(repository, "/suanyun-dev/") {
			imageType = enums.ImageTypeEnum.Base
		}
		//192.168.200.5/chenyu/public/c503abeeefb74af4ab4cb0e5948d4c56:v1.0.4-beta-20250115
		tmpAry := strings.Split(repository, "/")

		if len(tmpAry) == 4 {
			tmp := tmpAry[3]
			if strings.Contains(tmp, ":") {
				tAry := strings.Split(tmp, ":")
				if len(tAry) == 2 {
					imageName = tAry[0]
					imageTag = tAry[1]
				}
			}
		} else {
			if len(tmpAry) == 3 {
				imageName = tmpAry[2]
			}
			if item.Tag != "" {
				imageTag = item.Tag
			}
		}

		md5 := fmt.Sprintf("%d:%s:%s", imageType, imageName, imageTag)
		if imageType > 0 && imageName != "" && imageTag != "" {
			if val, ok := obj.Md5PodImageId.Load(md5); ok {
				if val.(string) != "" {
					aryPodImage = append(aryPodImage, val.(string))
				}
				continue
			} else {
				var podImage model.PodImage
				//logger.Info("开始查询镜像", "imageType:", imageType, "  imageName:", imageName, "  imageTag:", imageTag)
				searchCount++
				if err := podImage.GetByImageTypeAndTag(imageType, imageName, imageTag); err != nil {
					if err == gorm.ErrRecordNotFound {
						if imageType == enums.ImageTypeEnum.Private {
							imageType = enums.ImageTypeEnum.PrivateInstance
							searchCount++
							if err := podImage.GetByImageTypeAndTag(imageType, imageName, imageTag); err != nil {
								if err == gorm.ErrRecordNotFound {
									obj.Md5PodImageId.Store(md5, "")
								} else {
									hasError = true
									logger.Error(err, "imageType:", imageType, "  imageName:", imageName, "  imageTag:", imageTag)
								}
							} else {
								tmp := fmt.Sprintf("%d", podImage.ID)
								aryPodImage = append(aryPodImage, tmp)
								obj.Md5PodImageId.Store(md5, tmp)
							}
						}
					} else {
						hasError = true
						logger.Error(err, "imageType:", imageType, "  imageName:", imageName, "  imageTag:", imageTag)
					}
				} else {
					tmp := fmt.Sprintf("%d", podImage.ID)
					aryPodImage = append(aryPodImage, tmp)
					obj.Md5PodImageId.Store(md5, tmp)
				}
			}
		} else {
			//if repository != "" {
			//	logger.Error("未获取到相应信息 md5:", md5, "     ", "item:", utils.GetJsonFromStruct(item))
			//}
			//logger.Info("条件不充足，不查询 md5:", md5, "     ", "item:", utils.GetJsonFromStruct(item))
		}
	}
	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		logger.Error(err, " virtualId:", oReq.VirtualId)
		msg = err.Error()
		return
	} else {
		imagesIds := ""
		if len(aryPodImage) > 0 {
			imagesIds = "|" + strings.Join(aryPodImage, "|") + "|"
		} else {
			msg = "空数组"
		}
		if hasError == false {
			if err := virtual.SetImageIds(imagesIds); err != nil {
				logger.Error(err, " virtualId:", oReq.VirtualId)
				msg = err.Error()
				return
			}
		}
	}

	if virtual.ID > 0 {
		result["image_ids"] = virtual.ImageIds
		result["search_count"] = searchCount
		code = 0
	} else {
		msg = "虚拟机不存在"
	}

	//{"id":"4fe667ce8b38","repository":"hub.suanyun.cn/suanyun-dev/cuda","tag":"12.3.2-cudnn9-devel-ubuntu22.04-jupyter"}
}

func (obj *reportApi_) VirtualLocalNewImages(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq localImagesReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	//logger.Info("oReq.Image:", oReq.Images)

	ary := make([]uint, 0)
	if err := utils.GetStructAryFromJson(&ary, oReq.ImageIds); err != nil {
		logger.Error(err, "  oReq.ImageIds:", oReq.ImageIds)
		return
	}
	if len(ary) == 0 {
		msg = "上传Image数组为空"
		result["image_ids"] = ""
		code = 0
		return
	}

	var virtual model.Virtual
	addImagesId := make([]uint, 0)
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		logger.Error(err, " virtualId:", oReq.VirtualId)
		msg = err.Error()
		return
	} else {
		imageIds := virtual.ImageIds
		for _, val := range ary {
			tmp := fmt.Sprintf("|%d|", val)
			if !strings.Contains(imageIds, tmp) {
				if imageIds == "" {
					imageIds = "|"
				}
				addImagesId = append(addImagesId, val)
				imageIds += fmt.Sprintf("%d|", val)
			}
		}
		if virtual.ImageIds != imageIds {
			if err := virtual.SetImageIds(imageIds); err != nil {
				logger.Error(err, " virtualId:", oReq.VirtualId)
				msg = err.Error()
				return
			}
		}
	}

	result["image_ids"] = addImagesId
	code = 0

	//{"id":"4fe667ce8b38","repository":"hub.suanyun.cn/suanyun-dev/cuda","tag":"12.3.2-cudnn9-devel-ubuntu22.04-jupyter"}
}

func (obj *reportApi_) VirtualInspectImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq inspectImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("VirtualInspectImage oReq:", utils.GetJsonFromStruct(oReq))

	var podImage model.PodImage
	if err := podImage.GetById(oReq.ImageId); err != nil {
		msg = "获取镜像信息出错"
		logger.Error(msg, err)
		return
	}
	result["o_req"] = oReq
	if oReq.LayerCount > 0 {
		if err := podImage.SetLayerCount(oReq.LayerCount); err != nil {
			msg = "更新失败"
			logger.Error(msg, err)
			return
		} else {
			msg = "更新成功"
			code = 0
		}
	} else {
		msg = "层数为0"
		return
	}
}

func (obj *reportApi_) VirtualInfo(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq virtualInfoReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "查询虚拟机出错"
		logger.Error(msg, err, oReq)
		return
	}

	lastInitTime := time.Now()
	if oReq.LastInitTime.Time().After(jsontime.DefaultTime()) {
		lastInitTime = oReq.LastInitTime.Time()
	}

	m := map[string]interface{}{"free_gpus": oReq.GpuFrees, "total_gpus": oReq.TotalGpus, "total_instance": oReq.TotalInstance, "last_check_time": time.Now(), "timeout_at": oReq.TimeoutAt, "inited_at": oReq.InitAt, "last_init_time": lastInitTime, "docker_at": oReq.DockerAt}
	if err := virtual.Updates(m); err != nil {
		msg = "更新上报数据出错"
		logger.Error(msg, err, utils.GetJsonFromStruct(oReq))
		return
	} else {
		//logger.Info("更新虚拟机数据完成", utils.GetJsonFromStruct(oReq))
	}
	result["virtual_id"] = oReq.VirtualId
	result["virtual_host"] = virtual.Host
	msg = "上报成功"
	code = 0
}

func (obj *reportApi_) VirtualTimeout(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq virtualTimeoutReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "查询虚拟机出错"
		logger.Error(msg, err, oReq)
		return
	}
	if err := virtual.SetTimeoutAt(oReq.TimeoutAt); err != nil {
		msg = "更新超时时间出错"
		logger.Error(msg, err, oReq)
		return
	}
	msg = "超时上报成功"
	result["virtual_id"] = virtual.ID
	result["virtual_host"] = virtual.Host
	code = 0
}

func (obj *reportApi_) NodeInfo(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq nodeInfoReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var node model.Node
	if err := node.GetById(oReq.NodeId); err != nil {
		msg = "查询节点出错"
		logger.Error(msg, err, oReq)
		return
	}

	m := map[string]interface{}{"total_virtual": oReq.TotalVirtual, "free_gpus": oReq.FreeGpus, "total_gpus": oReq.TotalGpus, "total_instance": oReq.TotalInstance, "last_check_time": time.Now()}
	if err := node.Updates(m); err != nil {
		msg = "更新节点数据出错"
		logger.Error(msg, err, oReq)
		return
	}
	msg = "节点数据上报成功"
	result["node_id"] = node.ID
	code = 0
}

func (obj *reportApi_) StartupSuccess(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	logger.Info("ReportStartupSuccess")
	var oReq startupSuccessReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("ReportStartupSuccess", oReq)

	tmpJson := utils.GetJsonFromStruct(oReq.Docker)
	var tmpDocker service.Docker
	if err := utils.GetStructFromJson(&tmpDocker, tmpJson); err != nil || tmpDocker.ID == "" {
		msg = "数据解析失败"
		logger.Error(err, oReq)
		return
	}

	if tmpDocker.StartupMark != oReq.StartupMark {
		msg = "数据错误"
		logger.Error(msg, oReq)
		return
	}

	if str, err := service.InstanceNodeService.StartupSuccess(oReq.StartupMark, &tmpDocker); err != nil {
		msg = str
		logger.Error(msg, err, oReq)
		return
	} else {
		msg = str
		if msg == "" {
			msg = "启动完成上报成功"
		}
		result["startup_mark"] = oReq.StartupMark
		logger.Info("启动完成上报成功 ", str, " ", tmpDocker)
		go func() {
			var instance model.Instance
			if err := instance.GetByStartupMark(oReq.StartupMark); err != nil {
				msg = "获取实例信息失败"
				logger.Error(msg, err)
			} else {
				reason := "实例启动成功"
				if msg1, err1 := service.WarnService.InstanceRunning(instance.UserId, instance.Uuid, reason); err1 != nil {
					logger.Error("发送请求(实例运行提醒)失败 userId:", instance.ID, "  uuid:", instance.Uuid, "  reason:", reason, "  msg:", msg1, " err:", err1)
				} else {
					logger.Info("发送请求(实例运行提醒)成功 userId:", instance.ID, "  uuid:", instance.Uuid, "  reason:", reason)
				}
			}
		}()
		code = 0
	}
}

func (obj *reportApi_) StartupFail(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	logger.Info("StartupFail")
	var oReq startupSuccessReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("StartupFail", oReq)

	if oReq.StartupMark == "" {
		msg = "参数错误"
		return
	}

	tmpJson := utils.GetJsonFromStruct(oReq.Docker)
	var tmpDocker service.Docker
	if err := utils.GetStructFromJson(&tmpDocker, tmpJson); err != nil || tmpDocker.ID == "" {
		msg = "数据解析失败"
		logger.Error(err, oReq)
	}

	var instance model.Instance
	if err := instance.GetByStartupMark(oReq.StartupMark); err != nil {
		msg = err.Error()
		logger.Error(err, " ", oReq.StartupMark)
		return
	}

	if str, err := service.InstanceNodeService.StartupFail(instance.Uuid, instance.StartupMark, &tmpDocker, oReq.Reason); err != nil {
		msg = str
		logger.Error(msg, err, oReq)
		return
	} else {
		msg = str
		if msg == "" {
			msg = "启动失败上报成功"
		}
		result["startup_mark"] = oReq.StartupMark
		logger.Info("启动失败上报成功 ", str, " ", oReq.StartupMark)

		go func() {
			reason := "实例启动失败"
			if msg1, err1 := service.WarnService.InstanceRunning(instance.UserId, instance.Uuid, reason); err1 != nil {
				logger.Error("发送请求(实例运行提醒)失败 userId:", instance.ID, "  uuid:", instance.Uuid, "  reason:", reason, "  msg:", msg1, " err:", err1)
			} else {
				logger.Info("发送请求(实例运行提醒)成功 userId:", instance.ID, "  uuid:", instance.Uuid, "  reason:", reason)
			}
		}()

		code = 0
	}
}
